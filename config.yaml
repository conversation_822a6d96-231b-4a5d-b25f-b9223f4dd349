crawling:
  max_concurrent: 5
  rate_limit_per_domain: 2.0
  retry_attempts: 3
  cache_ttl: 3600
  user_agent: "Mozilla/5.0 (compatible; ResearchBot/1.0; +https://example.com/bot)"
  respect_robots_txt: true
  stealth_mode: true
  page_timeout: 30000

extraction:
  llm_provider: "openai"
  api_key: ""  # Will be overridden by environment variable
  extraction_timeout: 30
  chunk_size: 1000
  max_tokens: 2000
  model: "gpt-3.5-turbo"

indexing:
  elasticsearch_host: "http://localhost:9200"
  index_name: "research_content"
  index_refresh: "wait_for"
  bulk_size: 100
  replicas: 0
  shards: 1

redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""

monitoring:
  enable_metrics: true
  log_level: "DEBUG"
  prometheus_port: 9090
  log_file: "crawler.log"

security:
  ssl_verify: true
  proxy_enabled: false
  proxy_list: []
  ip_rotation_enabled: false 