import asyncio
from typing import List, Dict, Optional
from datetime import datetime
import logging
from urllib.parse import urlparse
from crawl4ai import AsyncWebCrawler
from .models import CrawlJob, ExtractedContent
from .config import Config

logger = logging.getLogger(__name__)

class MultiSiteCrawler:
    def __init__(self, config: Config):
        self.config = config
        self.max_concurrent = config.crawling.max_concurrent
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        self.rate_limiters = {}  # Domain -> RateLimiter
        self.session_cache = {}  # Domain -> Session
        
    async def initialize(self):
        """Initialize crawler with optimized config"""
        self.crawler_config = {
            'headless': True,
            'browser_type': 'chromium',
            'page_timeout': self.config.crawling.page_timeout,
            'verbose': True,
            'cache_mode': 'enabled',
            'session_reuse': True,
            'stealth_mode': self.config.crawling.stealth_mode,
            'user_agent': self.config.crawling.user_agent,
            'respect_robots_txt': self.config.crawling.respect_robots_txt,
            'retry_attempts': self.config.crawling.retry_attempts,
            'cache_ttl': self.config.crawling.cache_ttl,
            'extraction_config': {
                'strategy': 'LLMExtractionStrategy',
                'llm_provider': self.config.extraction.llm_provider,
                'api_key': self.config.extraction.api_key,
                'model': self.config.extraction.model,
                'max_tokens': self.config.extraction.max_tokens,
                'chunk_size': self.config.extraction.chunk_size,
                'timeout': self.config.extraction.extraction_timeout
            }
        }
        
    async def crawl_batch(self, jobs: List[CrawlJob]) -> List[Dict]:
        """Crawl multiple sites concurrently with rate limiting"""
        async with AsyncWebCrawler(**self.crawler_config) as crawler:
            tasks = []
            for job in jobs:
                task = self._crawl_single_with_limits(crawler, job)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return self._process_results(results, jobs)
    
    async def _crawl_single_with_limits(self, crawler, job: CrawlJob) -> Optional[ExtractedContent]:
        """Crawl single site with rate limiting and error handling"""
        domain = self._extract_domain(job.url)
        
        async with self.semaphore:
            # Apply rate limiting per domain
            await self._apply_rate_limit(domain)
            
            try:
                # Use cached session if available
                session_id = self.session_cache.get(domain)
                
                result = await crawler.arun(
                    url=job.url,
                    session_id=session_id,
                    extraction_strategy="LLMExtractionStrategy",
                    chunking_strategy="SlidingWindowChunking",
                    css_selector="article, .content, .post, main, .paper, .research-article, .publication",
                    exclude_external_links=True,
                    exclude_social_media_links=True,
                    extract_metadata=True,
                    extract_text=True,
                    extract_html=True,
                    extract_markdown=True,
                    extract_links=True,
                    extract_media=True,
                    follow_links=False
                )
                
                # Cache session for reuse
                if result.session_id:
                    self.session_cache[domain] = result.session_id
                
                return self._format_result(result, job)
                
            except Exception as e:
                logger.error(f"Error crawling {job.url}: {str(e)}")
                return None
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        return urlparse(url).netloc
    
    async def _apply_rate_limit(self, domain: str):
        """Apply rate limiting for domain"""
        if domain not in self.rate_limiters:
            self.rate_limiters[domain] = RateLimiter(
                requests_per_second=self.config.crawling.rate_limit_per_domain
            )
        await self.rate_limiters[domain].acquire()
    
    def _format_result(self, result, job: CrawlJob) -> ExtractedContent:
        """Format crawl result into ExtractedContent"""
        return ExtractedContent(
            url=job.url,
            title=result.metadata.get('title', ''),
            content=result.text,
            html=result.html,
            markdown=result.markdown,
            links=result.links,
            media=result.media,
            metadata={
                **result.metadata,
                'crawled_at': datetime.utcnow().isoformat(),
                'job_metadata': job.metadata
            }
        )
    
    def _process_results(self, results: List[Dict], jobs: List[CrawlJob]) -> List[ExtractedContent]:
        """Process crawl results and handle errors"""
        processed = []
        for result, job in zip(results, jobs):
            if isinstance(result, Exception):
                logger.error(f"Failed to crawl {job.url}: {str(result)}")
                continue
            if result:
                processed.append(result)
        return processed

class RateLimiter:
    def __init__(self, requests_per_second: float = 1.0):
        self.requests_per_second = requests_per_second
        self.min_interval = 1.0 / requests_per_second
        self.last_request = 0.0
    
    async def acquire(self):
        """Acquire permission to make request"""
        now = datetime.utcnow().timestamp()
        elapsed = now - self.last_request
        
        if elapsed < self.min_interval:
            sleep_time = self.min_interval - elapsed
            await asyncio.sleep(sleep_time)
        
        self.last_request = datetime.utcnow().timestamp() 