import asyncio
import logging
from typing import List, Optional
import redis.asyncio as redis
from datetime import datetime, timedelta, timezone
from .models import CrawlJob
from .config import Config

logger = logging.getLogger(__name__)

class CrawlScheduler:
    def __init__(self, config: Config):
        self.config = config
        redis_params = {
            'host': config.redis.host,
            'port': config.redis.port,
            'db': config.redis.db,
            'decode_responses': True
        }

        # Only add password if it's configured
        if config.redis.get_password():
            redis_params['password'] = config.redis.get_password()
            
        self.redis = redis.Redis(**redis_params)
        self.job_queue = "crawl_queue"
        self.processing_queue = "processing_queue"
        self.completed_set = "completed_jobs"
        
    async def initialize(self):
        """Initialize scheduler"""
        try:
            await self.redis.ping()
            logger.info("Connected to Redis successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise
        
    async def add_job(self, job: CrawlJob):
        """Add job to queue with priority"""
        try:
            # Check if already completed recently
            last_crawled = await self.redis.hget(self.completed_set, job.url)
            if last_crawled:
                last_crawled = datetime.fromisoformat(last_crawled)
                if datetime.now(timezone.utc) - last_crawled < job.crawl_interval:
                    logger.debug(f"Skipping {job.url} - crawled recently")
                    return

            # Add to queue with priority score
            score = self._calculate_priority(job)
            await self.redis.zadd(self.job_queue, {job.to_json(): score})
            logger.debug(f"Added job {job.url} with priority {score}")
            
        except Exception as e:
            logger.error(f"Failed to add job {job.url}: {str(e)}")
            raise
        
    async def get_next_batch(self, batch_size: int = 10) -> List[CrawlJob]:
        """Get next batch of jobs to process"""
        try:
            # Get highest priority jobs
            jobs_data = await self.redis.zrange(
                self.job_queue,
                0,
                batch_size - 1,
                desc=True,
                withscores=True
            )
            
            if not jobs_data:
                return []
                
            jobs = []
            for job_json, score in jobs_data:
                # Move to processing queue
                await self.redis.zrem(self.job_queue, job_json)
                await self.redis.zadd(self.processing_queue, {job_json: score})
                
                # Parse job
                job = CrawlJob.from_json(job_json)
                jobs.append(job)
                
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to get next batch: {str(e)}")
            return []
            
    async def mark_completed(self, job: CrawlJob):
        """Mark job as completed"""
        try:
            # Remove from processing queue
            await self.redis.zrem(
                self.processing_queue,
                job.to_json()
            )
            
            # Add to completed set with timestamp
            await self.redis.hset(
                self.completed_set,
                job.url,
                datetime.now(timezone.utc).isoformat()
            )
            
        except Exception as e:
            logger.error(f"Failed to mark job {job.url} as completed: {str(e)}")
            
    async def retry_failed(self, job: CrawlJob):
        """Retry failed job"""
        try:
            # Remove from processing queue
            await self.redis.zrem(
                self.processing_queue,
                job.to_json()
            )
            
            # Increment retry count
            job.retry_count += 1
            
            if job.retry_count < job.max_retries:
                # Add back to queue with lower priority
                score = self._calculate_priority(job)
                await self.redis.zadd(self.job_queue, {job.to_json(): score})
                logger.debug(f"Retrying job {job.url} (attempt {job.retry_count})")
            else:
                logger.error(f"Max retries exceeded for {job.url}")
                
        except Exception as e:
            logger.error(f"Failed to retry job {job.url}: {str(e)}")
            
    def _calculate_priority(self, job: CrawlJob) -> float:
        """Calculate job priority score"""
        base_priority = job.priority
        
        # Lower priority for retries
        retry_penalty = job.retry_count * 10
        
        # Higher priority for never crawled
        if not job.last_crawled:
            base_priority += 100
            
        # Higher priority for older content
        elif job.last_crawled:
            hours_old = (datetime.now(timezone.utc) - job.last_crawled).total_seconds() / 3600
            age_boost = min(hours_old / 24 * 10, 50)  # Max 50 point boost
            base_priority += age_boost
            
        return base_priority - retry_penalty 