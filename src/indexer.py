import logging
from typing import Dict, List, Any, Optional
from elasticsearch import AsyncElasticsearch
from datetime import datetime
import hashlib
from .models import ExtractedContent, SearchResult
from .config import Config

logger = logging.getLogger(__name__)

class RealTimeIndexer:
    def __init__(self, config: Config):
        self.config = config
        self.es = AsyncElasticsearch([config.indexing.elasticsearch_host])
        self.index_name = config.indexing.index_name
        
    async def initialize(self):
        """Initialize index with mapping"""
        try:
            # Check if index exists
            logger.info(f"Checking if index {self.index_name} exists...")
            exists = await self.es.indices.exists(index=self.index_name)
            logger.info(f"Index {self.index_name} exists: {exists}")
            
            if not exists:
                logger.info(f"Creating index {self.index_name}...")
                await self.create_index()
                logger.info(f"Created index {self.index_name}")
            else:
                logger.info(f"Index {self.index_name} already exists")
                
        except Exception as e:
            logger.error(f"Failed to initialize index: {str(e)}")
            raise
            
    async def create_index(self):
        """Create index with optimized mapping"""
        mapping = {
            "settings": {
                "number_of_shards": self.config.indexing.shards,
                "number_of_replicas": self.config.indexing.replicas
            },
            "mappings": {
                "properties": {
                    "url": {"type": "keyword"},
                    "title": {"type": "text"},
                    "content": {"type": "text"},
                    "summary": {"type": "text"},
                    "publication_date": {"type": "date"},
                    "author": {"type": "keyword"},
                    "topics": {"type": "keyword"},
                    "indexed_at": {"type": "date"}
                }
            }
        }
        
        await self.es.indices.create(
            index=self.index_name,
            body=mapping
        )
        
    async def index_content(self, content: ExtractedContent):
        """Index extracted content"""
        try:
            doc_id = self._generate_doc_id(content.url)
            doc = content.to_index_doc()
            
            await self.es.index(
                index=self.index_name,
                id=doc_id,
                body=doc,
                refresh=self.config.indexing.index_refresh
            )
            logger.debug(f"Indexed content from {content.url}")
            
        except Exception as e:
            logger.error(f"Failed to index content from {content.url}: {str(e)}")
            raise
            
    async def search(self, query: str, filters: Dict = None, size: int = 20) -> SearchResult:
        """Perform real-time search"""
        try:
            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["title^2", "content", "summary"],
                                    "type": "best_fields",
                                    "tie_breaker": 0.3
                                }
                            }
                        ]
                    }
                },
                "highlight": {
                    "fields": {
                        "title": {},
                        "content": {
                            "fragment_size": 150,
                            "number_of_fragments": 3
                        },
                        "summary": {
                            "number_of_fragments": 1
                        }
                    }
                },
                "size": size,
                "sort": [
                    {"_score": {"order": "desc"}},
                    {"indexed_at": {"order": "desc"}}
                ]
            }
            
            # Apply filters
            if filters:
                for field, value in filters.items():
                    search_body["query"]["bool"]["filter"] = search_body["query"]["bool"].get("filter", [])
                    if isinstance(value, list):
                        search_body["query"]["bool"]["filter"].append({
                            "terms": {field: value}
                        })
                    else:
                        search_body["query"]["bool"]["filter"].append({
                            "term": {field: value}
                        })
            
            response = await self.es.search(
                index=self.index_name,
                body=search_body
            )
            
            return SearchResult.from_es_response(response)
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            raise
            
    def _generate_doc_id(self, url: str) -> str:
        """Generate deterministic document ID from URL"""
        return hashlib.sha256(url.encode()).hexdigest() 